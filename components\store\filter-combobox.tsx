"use client"

import * as React from "react"
import { Check, ChevronsUpDown } from "lucide-react"
import { cn } from "@/lib/utils"
import { But<PERSON> } from "@/components/ui/button"
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from "@/components/ui/command"
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover"
import { Badge } from "@/components/ui/badge"
import Image from "next/image"

export interface FilterOption {
  value: string
  label: string
  icon?: string
  count?: number
}

interface FilterComboboxProps {
  options: FilterOption[]
  selected: string[]
  onSelectionChange: (selected: string[]) => void
  placeholder?: string
  title: string
  className?: string
  singleSelect?: boolean
}

export function FilterCombobox({
  options,
  selected,
  onSelectionChange,
  placeholder = "Select filters...",
  title,
  className,
  singleSelect = false
}: FilterComboboxProps) {
  const [open, setOpen] = React.useState(false)

  const handleSelect = (value: string) => {
    if (singleSelect) {
      // For single select, if clicking the same item, deselect it, otherwise select only this item
      const newSelected = selected.includes(value) ? [] : [value]
      onSelectionChange(newSelected)
    } else {
      // For multi-select, toggle the item
      const newSelected = selected.includes(value)
        ? selected.filter(item => item !== value)
        : [...selected, value]
      onSelectionChange(newSelected)
    }
  }

  const selectedCount = selected.length
  const displayTitle = singleSelect
    ? (selectedCount > 0 ? `${title}: ${options.find(opt => opt.value === selected[0])?.label || selected[0]}` : title)
    : (selectedCount > 0 ? `${title} +${selectedCount}` : title)

  return (
    <div className={cn("w-full", className)}>
      <Popover open={open} onOpenChange={setOpen}>
        <PopoverTrigger asChild>
          <Button
            variant="outline"
            role="combobox"
            aria-expanded={open}
            className="w-full justify-between bg-white/10 border-purple-500/30 text-white hover:bg-purple-600/30 hover:text-white hover:border-purple-400/50"
          >
            <div className="flex items-center space-x-2">
              <span>{displayTitle}</span>
              {selectedCount > 0 && !singleSelect && (
                <Badge variant="secondary" className="bg-purple-500/30 text-purple-200 text-xs">
                  {selectedCount}
                </Badge>
              )}
            </div>
            <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-[var(--radix-popover-trigger-width)] p-0 bg-black/90 border-purple-500/30">
          <Command className="bg-black/90 text-white [&_[cmdk-input-wrapper]]:border-purple-500/30 [&_[cmdk-input]]:text-white [&_[cmdk-input]]:placeholder:text-gray-400">
            <CommandInput 
              placeholder={`Search ${title.toLowerCase()}...`} 
              className="text-white placeholder:text-gray-400 border-purple-500/30" 
            />
            <CommandList className="bg-black/90">
              <CommandEmpty className="text-gray-400">No {title.toLowerCase()} found.</CommandEmpty>
              <CommandGroup>
                {options.map((option) => (
                  <CommandItem
                    key={option.value}
                    value={option.value}
                    onSelect={() => handleSelect(option.value)}
                    className="text-white hover:bg-purple-600/30 hover:text-white data-[selected=true]:bg-purple-600/30 data-[selected=true]:text-white cursor-pointer"
                  >
                    <Check
                      className={cn(
                        "mr-2 h-4 w-4 text-white",
                        selected.includes(option.value) ? "opacity-100" : "opacity-0"
                      )}
                    />
                    <div className="flex items-center justify-between w-full">
                      <div className="flex items-center space-x-2">
                        {option.icon && (
                          <Image
                            src={option.icon}
                            alt={option.label}
                            width={16}
                            height={16}
                            className="flex-shrink-0"
                          />
                        )}
                        <span>{option.label}</span>
                      </div>
                      {option.count !== undefined && (
                        <Badge variant="outline" className="text-xs text-gray-400 border-gray-600">
                          {option.count}
                        </Badge>
                      )}
                    </div>
                  </CommandItem>
                ))}
              </CommandGroup>
            </CommandList>
          </Command>
        </PopoverContent>
      </Popover>
    </div>
  )
}
